# Usage Examples

This document provides practical examples of using the MCP OpenAI Server.

## Basic Setup

1. **Set up your environment:**
   ```bash
   cp .env.example .env
   # Edit .env and add your OpenAI API key
   ```

2. **Install dependencies:**
   ```bash
   uv sync
   ```

3. **Test the server:**
   ```bash
   uv run python test_tool.py
   ```

## Claude Desktop Integration

### Configuration

Add this to your Claude Desktop config file:

```json
{
  "mcpServers": {
    "openai-chat": {
      "command": "uv",
      "args": [
        "--directory",
        "/Users/<USER>/path/to/mcp_openai",
        "run",
        "python",
        "run_server.py"
      ]
    }
  }
}
```

### Example Conversations

Once integrated with Claude Desktop, you can use the tool like this:

**Simple Question:**
```
User: What is machine learning?
Claude: I'll use the OpenAI chat tool to get information about machine learning.
[Uses openai_chat tool with message: "What is machine learning?"]
```

**Custom Instructions:**
```
User: Explain neural networks like I'm 10 years old
Claude: I'll ask for a simple explanation suitable for a child.
[Uses openai_chat tool with:
- message: "Explain neural networks"
- system_instructions: "Explain like I'm 10 years old. Use simple words and fun analogies."]
```

## MCP Inspector Testing

Test your server with the MCP Inspector:

```bash
# Install MCP Inspector (if not already installed)
npm install -g @modelcontextprotocol/inspector

# Run the inspector
npx @modelcontextprotocol/inspector uv run python run_server.py
```

This will open a web interface where you can:
- See available tools
- Test tool calls
- View server capabilities
- Debug issues

## Direct API Usage

You can also test the OpenAI client directly:

```python
import asyncio
from src.mcp_openai_server.config import get_settings
from src.mcp_openai_server.openai_client import OpenAIClient

async def test_direct():
    settings = get_settings()
    client = OpenAIClient(settings.openai_config)
    
    response = await client.chat_completion(
        message="Hello, how are you?",
        system_instructions="You are a friendly assistant."
    )
    print(response)

asyncio.run(test_direct())
```

## Common Use Cases

### 1. Research Assistant

Configure the server with research-focused system instructions:

```bash
# In .env file
SYSTEM_INSTRUCTIONS=You are a research assistant. Provide detailed, well-sourced information with citations when possible. Be thorough and academic in your responses.
```

### 2. Code Helper

Use custom instructions for programming help:

```json
{
  "name": "openai_chat",
  "arguments": {
    "message": "How do I implement a binary search in Python?",
    "system_instructions": "You are a senior software engineer. Provide clean, well-commented code examples with explanations."
  }
}
```

### 3. Creative Writing

For creative tasks:

```json
{
  "name": "openai_chat",
  "arguments": {
    "message": "Write a short story about a robot learning to paint",
    "system_instructions": "You are a creative writing assistant. Write engaging, descriptive prose with vivid imagery."
  }
}
```

### 4. Technical Documentation

For documentation help:

```json
{
  "name": "openai_chat",
  "arguments": {
    "message": "Document this API endpoint: POST /users",
    "system_instructions": "You are a technical writer. Create clear, comprehensive API documentation with examples."
  }
}
```

## Error Scenarios

### Invalid API Key
```bash
# Set invalid key in .env
OPENAI_API_KEY=invalid-key

# Run server - will show warning but continue
uv run python run_server.py
```

Response: `Error: Invalid or missing OpenAI API key`

### Rate Limiting
When you hit OpenAI's rate limits:

Response: `Error: OpenAI API rate limit exceeded`

### Empty Message
```json
{
  "name": "openai_chat",
  "arguments": {
    "message": ""
  }
}
```

Response: `Error: Message cannot be empty`

## Performance Tips

1. **Choose the right model:**
   - `gpt-4o-mini`: Fast and cost-effective for most tasks
   - `gpt-4o`: Best quality for complex reasoning
   - `gpt-3.5-turbo`: Fastest for simple tasks

2. **Optimize token usage:**
   ```bash
   # Reduce max tokens for shorter responses
   OPENAI_MAX_TOKENS=500
   ```

3. **Adjust temperature:**
   ```bash
   # Lower temperature for factual responses
   OPENAI_TEMPERATURE=0.1
   
   # Higher temperature for creative tasks
   OPENAI_TEMPERATURE=0.9
   ```

## Monitoring and Debugging

### Enable Debug Logging

Modify `src/mcp_openai_server/server.py`:

```python
logging.basicConfig(
    level=logging.DEBUG,  # Change from INFO to DEBUG
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    stream=sys.stderr
)
```

### Check Server Health

```bash
# Test configuration
uv run python -c "from src.mcp_openai_server.config import get_settings; print('Config OK')"

# Test OpenAI connection
uv run python test_tool.py
```

### Monitor Usage

Check your OpenAI dashboard for:
- API usage statistics
- Cost tracking
- Rate limit status
- Error rates

## Integration with Other Tools

### Custom MCP Client

You can build your own MCP client to use this server:

```python
import asyncio
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

async def main():
    server_params = StdioServerParameters(
        command="uv",
        args=["run", "python", "run_server.py"]
    )
    
    async with stdio_client(server_params) as (read, write):
        async with ClientSession(read, write) as session:
            # Initialize
            await session.initialize()
            
            # List tools
            tools = await session.list_tools()
            print(f"Available tools: {[tool.name for tool in tools.tools]}")
            
            # Call tool
            result = await session.call_tool(
                "openai_chat",
                {"message": "Hello from custom client!"}
            )
            print(f"Response: {result.content[0].text}")

asyncio.run(main())
```

## Troubleshooting Guide

### Server Won't Start

1. Check Python version: `python --version` (should be 3.10+)
2. Verify dependencies: `uv sync`
3. Check .env file exists and has valid API key
4. Look at error messages in stderr

### Tool Not Available in Claude Desktop

1. Verify server starts without errors
2. Check Claude Desktop config syntax
3. Use absolute paths in configuration
4. Restart Claude Desktop
5. Check Claude Desktop logs

### API Errors

1. Verify API key is correct
2. Check OpenAI account credits
3. Monitor rate limits
4. Check model availability

### Performance Issues

1. Reduce `OPENAI_MAX_TOKENS`
2. Use faster models like `gpt-4o-mini`
3. Optimize system instructions length
4. Monitor network connectivity
