"""Main MCP server implementation."""

import asyncio
import logging
import sys
from typing import Any, Optional

from mcp.server.fastmcp import FastMCP
from mcp.server.models import InitializationOptions
from mcp.types import <PERSON><PERSON>, TextContent

from .config import get_settings
from .openai_client import OpenAIClient, OpenAIClientError

# Configure logging to stderr (not stdout for MCP servers)
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    stream=sys.stderr
)
logger = logging.getLogger(__name__)

# Initialize settings and clients
try:
    settings = get_settings()
    openai_client = OpenAIClient(settings.openai_config)
except Exception as e:
    logger.error(f"Configuration error: {str(e)}")
    sys.exit(1)

# Initialize FastMCP server
mcp = FastMCP(settings.mcp_config.name)


@mcp.tool()
async def openai_chat(
    message: str,
    system_instructions: Optional[str] = None
) -> str:
    """Send a message to OpenAI's chat API with optional system instructions.

    This tool combines user input with server-side system instructions
    and calls OpenAI's API for chat completion. It provides a simple interface
    for clients to interact with OpenAI's language models through the MCP protocol.

    Args:
        message: The user message to send to the AI. This should contain the main
                query or request that you want the AI to respond to.
        system_instructions: Optional custom system instructions to override the
                           default server instructions. Use this to customize the
                           AI's behavior for specific requests.

    Returns:
        The AI's response as a string. If an error occurs, it will be prefixed
        with "Error: " followed by a description of the issue.

    Examples:
        - Basic usage: openai_chat("What is the capital of France?")
        - With custom instructions: openai_chat("Explain quantum physics",
          "You are a physics professor. Explain concepts simply for beginners.")
    """
    try:
        logger.info(f"Processing chat request with message length: {len(message)}")

        # Validate input
        if not message or not message.strip():
            return "Error: Message cannot be empty"

        # Use provided system instructions or fall back to default
        instructions = system_instructions or settings.system_instructions

        logger.debug(f"Using system instructions: {instructions[:100]}...")

        # Call OpenAI API
        response = await openai_client.chat_completion(
            message=message.strip(),
            system_instructions=instructions
        )

        logger.info("Successfully processed chat request")
        return response

    except OpenAIClientError as e:
        error_msg = f"OpenAI API error: {str(e)}"
        logger.error(error_msg)
        return f"Error: {error_msg}"

    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        logger.error(error_msg)
        return f"Error: {error_msg}"


async def main() -> None:
    """Main entry point for the MCP server."""
    try:
        logger.info(f"Starting MCP OpenAI Server v{settings.mcp_config.version}")
        logger.info(f"Using OpenAI model: {settings.openai_config.model}")

        # Test OpenAI connection
        logger.info("Testing OpenAI API connection...")
        if await openai_client.test_connection():
            logger.info("OpenAI API connection successful")
        else:
            logger.warning("OpenAI API connection test failed - server will still start")

        # Run the MCP server
        logger.info("Starting MCP server...")
        await mcp.run(transport="stdio")

    except KeyboardInterrupt:
        logger.info("Server shutdown requested")
    except Exception as e:
        logger.error(f"Server error: {str(e)}")
        sys.exit(1)


def run_server():
    """Run the server, handling asyncio properly."""
    try:
        # Check if we're already in an asyncio event loop
        loop = asyncio.get_running_loop()
        logger.error("Cannot run server: already in an asyncio event loop")
        logger.info("This usually happens when the server is called from within another async context")
        logger.info("Try running the server directly: python -m src.mcp_openai_server.server")
        sys.exit(1)
    except RuntimeError:
        # No event loop running, safe to use asyncio.run()
        asyncio.run(main())


if __name__ == "__main__":
    run_server()
